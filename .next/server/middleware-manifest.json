{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "af62723e3c1dac154ac966d4cc5292b0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f513dd1df27a7c694da27c6584dff8d3bc2eabf495915bd80715cc8c6547c855", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d6541786dc22b13027c0b82568beb9ef53b416eefe938aee5390ff0cfea3e5d4"}}}, "sortedMiddleware": ["/"], "functions": {}}